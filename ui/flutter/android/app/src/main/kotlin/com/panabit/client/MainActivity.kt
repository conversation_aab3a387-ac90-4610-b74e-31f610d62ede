/**
 * FILE: MainActivity.kt
 *
 * DESCRIPTION:
 *     ITforce WAN Android主活动，继承FlutterActivity提供Flutter UI容器。
 *     负责初始化Platform Channel通信和应用程序生命周期管理。
 *     实现延迟初始化模式，遵循iOS初始化时序。
 *
 * AUTHOR: wei
 * HISTORY: 23/06/2025 create, 23/06/2025 add delayed initialization
 */

package com.panabit.client

import android.app.Activity
import android.content.Intent
import android.os.Bundle
import androidx.lifecycle.lifecycleScope
import io.flutter.embedding.android.FlutterActivity
import io.flutter.plugin.common.MethodChannel
import kotlinx.coroutines.launch
import com.panabit.client.presentation.flutter.PlatformChannelHandler
import com.panabit.client.domain.adapters.LazyVPNServiceAdapter
import com.panabit.client.vpn.ITforceVPNService
import com.panabit.client.infrastructure.logging.logDebug
import com.panabit.client.infrastructure.logging.logInfo
import com.panabit.client.infrastructure.logging.logWarn
import com.panabit.client.infrastructure.logging.logError
import com.panabit.client.infrastructure.RoutingConfigurationManager
import org.koin.android.ext.android.inject

/**
 * NAME: MainActivity
 *
 * DESCRIPTION:
 *     ITforce WAN Android主活动，继承FlutterActivity。
 *     提供Flutter UI容器，处理应用程序生命周期。
 *     实现延迟初始化Platform Channel Handler，遵循iOS模式。
 *
 * PROPERTIES:
 *     platformChannelHandler - Platform Channel处理器（延迟初始化）
 */
class MainActivity: FlutterActivity() {

    // Inject RoutingConfigurationManager using Koin
    private val routingConfigurationManager: RoutingConfigurationManager by inject()

    companion object {
        private var instance: MainActivity? = null

        /**
         * NAME: getPlatformChannelHandler
         *
         * DESCRIPTION:
         *     获取当前活动的PlatformChannelHandler实例。
         *     用于ITforceVPNService向Flutter发送状态更新。
         *
         * RETURNS:
         *     PlatformChannelHandler? - 当前的处理器实例或null
         */
        fun getPlatformChannelHandler(): PlatformChannelHandler? {
            return instance?.platformChannelHandler
        }
    }

    private var platformChannelHandler: PlatformChannelHandler? = null

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        instance = this
        setupInitializationChannel()  // 只设置初始化Channel
        setupSystemChannel()  // 设置系统操作Channel
        logInfo("MainActivity created with initialization and system channels setup")
    }

    /**
     * NAME: setupInitializationChannel
     *
     * DESCRIPTION:
     *     设置初始化Channel，等待Flutter UI发起初始化请求。
     *     遵循iOS延迟初始化模式，避免启动时创建VPN服务。
     */
    private fun setupInitializationChannel() {
        val flutterEngine = flutterEngine
        if (flutterEngine == null) {
            logError("FlutterEngine is null, cannot setup initialization channel")
            return
        }

        val initChannel = MethodChannel(
            flutterEngine.dartExecutor.binaryMessenger,
            "panabit_client/init"
        )

        initChannel.setMethodCallHandler { call, result ->
            if (call.method == "initializeBackend") {
                logInfo("Received initializeBackend request from Flutter")

                // 用户登录时才创建完整的PlatformChannelHandler
                val handler = setupPlatformChannelHandler()
                if (handler != null) {
                    // 处理初始化请求 - 检测后端是否初始化成功
                    lifecycleScope.launch {
                        try {
                            val success = handler.handleInitializeBackend()
                            result.success(success)
                            logInfo("Backend initialization completed", mapOf("success" to success))
                        } catch (e: Exception) {
                            logError("Backend initialization failed", e)
                            result.error("INIT_FAILED", "Backend initialization failed: ${e.message}", null)
                        }
                    }
                } else {
                    logError("Failed to setup PlatformChannelHandler")
                    result.error("INIT_FAILED", "Handler creation failed", null)
                }
            } else {
                result.notImplemented()
            }
        }

        logInfo("Initialization channel configured: panabit_client/init")
    }

    /**
     * NAME: setupSystemChannel
     *
     * DESCRIPTION:
     *     设置系统操作Channel，处理应用最小化等系统级操作
     */
    private fun setupSystemChannel() {
        val flutterEngine = flutterEngine
        if (flutterEngine == null) {
            logError("FlutterEngine is null, cannot setup system channel")
            return
        }

        val systemChannel = MethodChannel(
            flutterEngine.dartExecutor.binaryMessenger,
            "panabit_client/system"
        )

        systemChannel.setMethodCallHandler { call, result ->
            when (call.method) {
                "minimizeApp" -> {
                    logInfo("Received minimizeApp request from Flutter")
                    try {
                        // 将应用移到后台而不是退出
                        moveTaskToBack(true)
                        result.success(true)
                        logInfo("App moved to background successfully")
                    } catch (e: Exception) {
                        logError("Failed to move app to background", e)
                        result.error("MINIMIZE_FAILED", "Failed to minimize app: ${e.message}", null)
                    }
                }
                "installApk" -> {
                    logInfo("Received installApk request from Flutter")
                    handleInstallApk(call.arguments, result)
                }
                else -> {
                    result.notImplemented()
                }
            }
        }

        logInfo("System channel configured: panabit_client/system")
    }

    /**
     * NAME: setupPlatformChannelHandler
     *
     * DESCRIPTION:
     *     创建并配置PlatformChannelHandler。
     *     用户登录时调用，创建完整的Platform Channel通信。
     *     遵循iOS延迟初始化模式，PlatformChannelHandler创建时不要求VPN服务已运行。
     *
     * RETURNS:
     *     PlatformChannelHandler? - 创建的处理器或null如果失败
     */
    private fun setupPlatformChannelHandler(): PlatformChannelHandler? {
        if (platformChannelHandler != null) {
            logInfo("Reusing existing PlatformChannelHandler")
            return platformChannelHandler
        }

        val flutterEngine = flutterEngine
        if (flutterEngine == null) {
            logError("FlutterEngine is null, cannot setup PlatformChannelHandler")
            return null
        }

        return try {
            // 遵循iOS模式：创建一个延迟初始化的VPN服务适配器
            // VPN服务将在第一次方法调用时按需启动
            val serviceAdapter = LazyVPNServiceAdapter(this)

            // 创建Platform Channel Handler with RoutingConfigurationManager injection
            val handler = PlatformChannelHandler(serviceAdapter, this, routingConfigurationManager)
            handler.configureFlutter(flutterEngine.dartExecutor.binaryMessenger)

            platformChannelHandler = handler
            logInfo("PlatformChannelHandler configured successfully with lazy initialization")

            handler
        } catch (e: Exception) {
            logError("Failed to setup PlatformChannelHandler", e)
            null
        }
    }

    /**
     * NAME: onActivityResult
     *
     * DESCRIPTION:
     *     Handles activity results for permission requests.
     *     Forwards VPN permission results to PermissionManager for proper callback handling.
     *
     * PARAMETERS:
     *     requestCode - Request code from activity result
     *     resultCode - Result code from activity result
     *     data - Intent data from activity result
     */
    @Deprecated("Deprecated in Java")
    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)

        // Handle VPN permission result
        if (requestCode == 1001) { // VPN_PERMISSION_REQUEST_CODE from PlatformChannelHandler
            val granted = resultCode == RESULT_OK
            logInfo(
                message = "VPN permission result received",
                context = mapOf(
                    "granted" to granted,
                    "resultCode" to resultCode
                )
            )

            // Send permission result event to Flutter
            platformChannelHandler?.let { handler ->
                val eventData = mapOf(
                    "granted" to granted,
                    "status" to if (granted) "authorized" else "denied",
                    "message" to if (granted) "VPN权限已授权" else "VPN权限被拒绝"
                )
                handler.sendEvent("vpn_permission_result", eventData)
            }
        }

        // Handle battery optimization permission result
        if (requestCode == 1002) { // BATTERY_OPTIMIZATION_REQUEST_CODE from PermissionManager
            logInfo(
                message = "Battery optimization permission result received",
                context = mapOf(
                    "resultCode" to resultCode
                )
            )

            // Check if battery optimization is now ignored
            platformChannelHandler?.let { handler ->
                handler.checkBatteryOptimizationStatus()
            }
        }

        // Also forward to PermissionManager for backward compatibility
        platformChannelHandler?.let { handler ->
            handler.handleActivityResult(requestCode, resultCode)
        }

        logDebug(
            message = "Activity result handled",
            context = mapOf(
                "requestCode" to requestCode,
                "resultCode" to resultCode
            )
        )
    }

    override fun onDestroy() {
        super.onDestroy()

        // Cleanup platform channel handler resources
        platformChannelHandler?.cleanup()
        platformChannelHandler = null

        // Clear instance reference
        if (instance == this) {
            instance = null
        }

        logInfo("MainActivity destroyed, PlatformChannelHandler cleaned up")
    }

    /**
     * NAME: handleInstallApk
     *
     * DESCRIPTION:
     *     处理APK安装请求，使用FileProvider安全地共享APK文件
     *
     * PARAMETERS:
     *     arguments - 包含APK文件路径的参数
     *     result - Flutter方法调用结果回调
     */
    private fun handleInstallApk(arguments: Any?, result: MethodChannel.Result) {
        logInfo("Handling APK installation request", mapOf("arguments" to arguments.toString()))

        try {
            val args = arguments as? Map<String, Any>
            val filePath = args?.get("filePath") as? String

            if (filePath == null) {
                logError("APK installation failed: file path is null", mapOf("arguments" to arguments.toString()))
                result.error("INVALID_ARGUMENTS", "File path is required", null)
                return
            }

            val file = java.io.File(filePath)
            if (!file.exists()) {
                logError("APK installation failed: file not found", mapOf("filePath" to filePath))
                result.error("FILE_NOT_FOUND", "APK file not found: $filePath", null)
                return
            }

            // 使用FileProvider获取content URI
            val authority = "${packageName}.fileprovider"
            val contentUri = androidx.core.content.FileProvider.getUriForFile(
                this,
                authority,
                file
            )

            logInfo("Generated content URI for APK", mapOf(
                "filePath" to filePath,
                "authority" to authority,
                "contentUri" to contentUri.toString()
            ))

            // 创建安装Intent
            val installIntent = android.content.Intent(android.content.Intent.ACTION_VIEW).apply {
                setDataAndType(contentUri, "application/vnd.android.package-archive")
                addFlags(android.content.Intent.FLAG_ACTIVITY_NEW_TASK)
                addFlags(android.content.Intent.FLAG_GRANT_READ_URI_PERMISSION)
            }

            // 检查是否有应用可以处理此Intent
            if (installIntent.resolveActivity(packageManager) != null) {
                startActivity(installIntent)
                logInfo("APK installation intent launched successfully", mapOf("filePath" to filePath))

                // 返回状态信息，告知Flutter安装程序已启动
                val response = mapOf(
                    "success" to true,
                    "status" to "install_started",
                    "message" to "install_started" // 使用标识符，Flutter端会进行国际化处理
                )
                logInfo("Returning APK installation response", mapOf("response" to response.toString()))
                result.success(response)
            } else {
                logError("No application can handle APK installation", mapOf("filePath" to filePath))
                result.error("NO_HANDLER", "No application can handle APK installation", null)
            }

        } catch (e: Exception) {
            logError("APK installation failed with exception", mapOf("arguments" to arguments.toString()), e)
            result.error("INSTALL_FAILED", "Failed to install APK: ${e.message}", null)
        }
    }
}
