{"@@locale": "en", "appTitle": "Panabit iWAN", "login": "<PERSON><PERSON>", "username": "Username", "password": "Password", "domain": "Domain", "rememberCredentials": "Remember credentials", "loginButton": "<PERSON><PERSON>", "loginFailed": "<PERSON><PERSON> failed", "loginSuccess": "Login successful", "logout": "Logout", "connect": "Connect", "disconnect": "Disconnect", "connecting": "Connecting...", "connected": "Connected", "disconnected": "Not Connected", "disconnecting": "Disconnecting...", "reconnecting": "Reconnecting...", "connectionFailed": "Connection failed", "connectionSuccess": "Connection successful", "serverList": "Server List", "selectServer": "Select Server", "noServersAvailable": "No servers available", "refreshServers": "Refresh Servers", "settings": "Settings", "about": "About", "statistics": "Statistics", "logs": "Logs", "language": "Language", "theme": "Theme", "autoConnect": "Auto Connect", "minimizeToTray": "Minimize to Tray", "startWithSystem": "Start with System", "connectionTime": "Connection Time", "dataTransferred": "Data Transferred", "ping": "<PERSON>", "latency": "Latency", "version": "Version", "copyright": "Copyright", "termsOfService": "Terms of Service", "privacyPolicy": "Privacy Policy", "acceptLicense": "I accept the Terms of Service and Privacy Policy", "licenseRequired": "You must accept the license agreement to continue", "error": "Error", "warning": "Warning", "info": "Information", "success": "Success", "cancel": "Cancel", "ok": "OK", "yes": "Yes", "no": "No", "save": "Save", "close": "Close", "exit": "Exit", "minimize": "Minimize", "maximize": "Maximize", "restore": "Rest<PERSON>", "copy": "Copy", "paste": "Paste", "cut": "Cut", "selectAll": "Select All", "clear": "Clear", "refresh": "Refresh", "retry": "Retry", "loading": "Loading...", "pleaseWait": "Please wait...", "networkError": "Network error", "serverError": "Server error", "connectionTimeout": "Connection timeout", "invalidCredentials": "Invalid credentials", "sessionExpired": "Session expired", "accessDenied": "Access denied", "serviceUnavailable": "Service unavailable", "mainScreen": "Main", "connectionScreen": "Connection", "userScreen": "User", "settingsScreen": "Settings", "aboutScreen": "About", "logsScreen": "Logs", "statisticsScreen": "Statistics", "enterServerDomain": "Enter Server Domain", "serverDomain": "Server Domain", "serverDomainHint": "e.g.: vpn.example.com", "nextStep": "Next", "clientDomain": "Client Domain", "change": "Change", "rememberUsernamePassword": "Remember username and password", "pleaseEnterServerDomain": "Please enter server domain", "pleaseEnterUsername": "Please enter username", "pleaseEnterPassword": "Please enter password", "pleaseEnterClientDomain": "Please enter client domain", "clientDomainHint": "e.g.: research.staff.unisase", "lookupServiceError": "Failed to query server address", "lookupServiceTimeout": "Server address query timeout", "lookupServiceInvalidResponse": "Invalid response from server", "lookupServiceNetworkError": "Network connection failed, please check network settings", "queryingServerAddress": "Querying server address...", "startingBackendService": "Starting backend service...", "backendServiceStartFailed": "Failed to start backend service, please check permissions or restart application", "checkingBackendServiceStatus": "Starting...", "backendServiceHealthCheckFailed": "Backend service health check failed, please check if port is occupied or restart application", "autoStart": "Auto Start", "autoStartEnabled": "Auto start enabled", "autoStartDisabled": "Auto start disabled", "routingSettings": "Routing <PERSON><PERSON><PERSON>", "applying": "Applying...", "applySettings": "Apply Settings", "settingsAppliedSuccessfully": "Settings applied successfully", "noChangesToApply": "No changes to apply", "getRoutingSettingsFailed": "Failed to get routing settings", "saveAutoStartSettingFailed": "Failed to save auto start setting", "saveRoutingSettingsFailed": "Failed to save routing settings", "appSettings": "App Settings", "appName": "Application Name", "versionNumber": "Version", "deviceId": "Device ID", "gettingDeviceId": "Getting...", "getDeviceIdFailed": "Failed to get", "agreementsAndContact": "Agreements", "viewTermsOfService": "View Terms of Service", "viewPrivacyPolicy": "View Privacy Policy", "officialWebsite": "Official Website", "technicalSupport": "Technical Support", "openLinkFailed": "Failed to open link", "clickToView": "Click to view", "sendEmailFailed": "Failed to send email", "vpnClientFeedback": "WAN Client Feedback", "allUrlLaunchMethodsFailed": "All URL launch methods failed", "openLinkFailedWithError": "Failed to open link", "sendEmailFailedWithError": "Failed to send email", "statisticsInfo": "Statistics", "status": "Status", "interface": "Interface", "upload": "Upload", "download": "Download", "localIp": "Local IP", "itforceIp": "Cloud IP", "personalInfo": "Personal Information", "editPersonalInfo": "Edit Personal Information", "name": "Name", "pleaseEnterName": "Please enter name", "department": "Department", "position": "Position", "accountInfo": "Account Information", "clientDomainLabel": "Client Domain", "usernameLabel": "Username", "deviceInfo": "Device ID", "logoutButton": "Logout", "personalInfoSaved": "Personal information saved", "saveFailed": "Save failed, please try again", "notSet": "Not set", "editUserInfo": "Edit User Info", "confirmLogout": "Confirm <PERSON>ut", "logoutConfirmation": "Are you sure you want to logout?", "logoutWithVpnWarning": "Are you sure you want to logout?\n\nNote: Current WAN connection will be automatically disconnected.", "disconnectAndExit": "Disconnect and Exit", "clearLogs": "Clear Logs", "confirmClearLogs": "Are you sure you want to clear all logs?", "confirm": "Confirm", "confirmReconnection": "Confirm Reconnection", "routingChangeRequiresReconnection": "Current VPN is connected. Changing routing settings requires reconnection. Do you want to confirm and reconnect?", "confirmAndReconnect": "Confirm and Reconnect", "routingSettingsAppliedAndReconnected": "Routing settings saved and reconnected", "routingSettingsAppliedDisconnected": "Routing settings saved, VPN disconnected", "routingSettingsReconnectionFailed": "Routing settings reconnection failed", "logsExportedTo": "Logs exported to", "exportLogsFailed": "Failed to export logs", "logCopiedToClipboard": "Log copied to clipboard", "allLevels": "All Levels", "searchLogs": "Search logs...", "logsTitle": "Logs", "closeSearch": "Close Search", "searchLogsTooltip": "Search Logs", "filterByLevel": "Filter by Level", "moreActions": "More Actions", "exportLogs": "Export Logs", "filterPrefix": "Filter: ", "clearFilter": "Clear Filter", "noLogs": "No logs", "noData": "No data", "scrollToLatestLog": "Scroll to Latest Log", "connectionManagement": "Connection Management", "userInfo": "User Information", "trafficStatistics": "Traffic Statistics", "systemLogs": "System Logs", "aboutApp": "About App", "connection": "Connection", "user": "User", "statisticsNav": "Statistics", "settingsNav": "Settings", "logsNav": "Logs", "aboutNav": "About", "auto": "Auto", "statusUpdated": "Status updated", "routingMode": "Routing Mode", "allRouting": "All Routing", "allRoutingDescription": "All traffic goes through WAN tunnel", "customRouting": "Custom Routing", "customRoutingDescription": "Only specified network segments go through WAN tunnel", "enterNetworkSegments": "Please enter network segments to route", "networkSegmentsExample": "Separate multiple segments with commas, e.g.: ***********/16,10.0.0.0/8", "enterNetworkSegmentsHint": "Enter network segments...", "ensureCorrectCidrFormat": "Please ensure correct CIDR format", "uploadSpeed": "Upload", "downloadSpeed": "Download", "unreachable": "Unreachable", "excellent": "Excellent", "good": "Good", "poor": "Poor", "languageSettings": "Language Settings", "selectLanguage": "Select Language", "chinese": "中文", "english": "English", "languageChanged": "Language changed", "pleaseSelectServer": "Please select a server first", "connectingToServer": "Connecting...", "disconnectingFromServer": "Disconnecting...", "connectionTimeoutDetailed": "Connection timeout, please check network connection or try again later", "connectionFailedGeneric": "Connection failed", "disconnectedFromServer": "Disconnected", "switchingToServer": "Switching to {serverName}...", "connectedToServer": "Connected to {serverName}", "currentlyConnectedTo": "Currently connected to {serverName}, switching to {newServerName}", "selectServerFirst": "Please select a server first", "operationTimeout": "Operation timeout, please try again later", "pingServersFailed": "Ping servers failed: {error}", "networkConnectionFailed": "Network connection failed, please check network settings", "realtimeConnectionInterrupted": "Real-time connection interrupted, attempting to reconnect...", "authenticationFailed": "Authentication failed, please login again", "operationFailedRetry": "Operation failed, please try again later", "systemTrayConnected": "Connected", "systemTrayConnecting": "Connecting...", "systemTrayDisconnecting": "Disconnecting...", "systemTrayDisconnected": "Disconnected", "showWindow": "Show Window", "hideWindow": "Hide Window", "exitApp": "Exit App", "processing": "Processing...", "calculating": "Calculating...", "operationCancelled": "Operation cancelled", "testLatency": "Test Latency", "testingLatency": "Testing latency...", "latencyTestComplete": "Latency test complete", "currentlyConnectedToServer": "Currently connected to: {serverName}", "unknownServer": "Unknown server", "noAutoServersAvailable": "No auto servers available, please check network connection or contact administrator", "autoServerSelectionFailed": "Auto server selection failed, please select server manually or try again later", "apiInvalidRequest": "Invalid request format or parameters", "apiInvalidCredentials": "Invalid username or password", "apiServerError": "Server internal error", "apiResourceNotFound": "Requested resource not found", "apiUnauthorized": "Unauthorized access, please login again", "apiForbidden": "Access to this resource is forbidden", "apiTimeout": "Request timeout, please try again later", "apiConflict": "Resource conflict", "apiRateLimit": "Too many requests, please try again later", "apiGatewayError": "Gateway error", "apiServiceUnavailable": "Service temporarily unavailable, please try again later", "networkUnreachable": "Network unreachable, please check network connection", "networkDnsFailure": "DNS resolution failed, please check server address", "networkConnectionReset": "Network connection was reset", "networkConnectionClosed": "Network connection was closed", "networkProxyError": "Proxy server error", "networkTlsError": "TLS/SSL error", "authInvalidCredentials": "Invalid user credentials", "authExpiredCredentials": "Credentials expired, please login again", "authRateLimit": "Authentication requests too frequent, please try again later", "authAccountLocked": "Account locked, please contact administrator", "authInvalidToken": "Invalid authentication token", "authTokenExpired": "Authentication token expired, please login again", "authMissingCredentials": "Missing authentication credentials", "tunnelError": "Tunnel error", "tunnelInitFailed": "Tunnel initialization failed", "tunnelCloseFailed": "Tunnel close failed", "tunnelReadFailed": "Tunnel read failed", "tunnelWriteFailed": "Tunnel write failed", "tunnelConfigFailed": "Tunnel configuration failed", "configError": "Configuration error", "configInvalid": "Invalid configuration", "configFileNotFound": "Configuration file not found", "configFileReadFailed": "Configuration file read failed", "configFileWriteFailed": "Configuration file write failed", "configFileParseFailed": "Configuration file parse failed", "platformError": "Platform error", "platformUnsupported": "Unsupported platform", "platformInitFailed": "Platform initialization failed", "platformIoError": "Platform IO error", "platformPermissionDenied": "Permission denied, please run as administrator", "domainLookupFailed": "Domain lookup failed", "domainNotFound": "Domain not found, please check the domain name", "domainInvalid": "Invalid domain format", "domainRequired": "Domain parameter is required", "domainLookupTimeout": "Domain lookup timeout, please try again later", "domainLookupNetworkError": "Network error during domain lookup, please check your connection", "serverListNotFound": "Server list not found, please check the server URL", "serverListInvalid": "Invalid server list format", "serverListTimeout": "Server list request timeout, please try again later", "serverListNetworkError": "Network error while fetching server list, please check your connection", "protocolError": "Protocol error", "protocolInvalid": "Invalid protocol", "protocolUnsupported": "Unsupported protocol", "protocolVersionMismatch": "Protocol version mismatch", "protocolHandshakeFailed": "Protocol handshake failed", "protocolEncryptionFailed": "Protocol encryption failed", "protocolDecryptionFailed": "Protocol decryption failed", "unknownError": "Unknown error", "invalidParameter": "Invalid parameter", "notImplemented": "Feature not implemented", "permissionDenied": "Permission denied", "checkUpdate": "Check Update", "checkingUpdate": "Checking for updates...", "updateAvailable": "Update Available", "updateNotAvailable": "No updates available", "currentVersion": "Current Version", "latestVersion": "Latest Version", "updateNow": "Update Now", "updateLater": "Re<PERSON> Later", "skipUpdate": "Skip Update", "downloading": "Downloading...", "downloadProgress": "Download Progress", "downloadComplete": "Download Complete", "downloadFailed": "Download Failed", "installing": "Installing...", "installComplete": "Installation Complete", "installFailed": "Installation Failed", "installStarted": "Installation started. Please tap 'Install' in the system dialog to complete the app update. Please restart the app manually after installation.", "updateFailed": "Update Failed", "updateCancelled": "Update Cancelled", "forceUpdate": "Force Update", "forceUpdateMessage": "This update contains critical security fixes and must be installed immediately", "releaseNotes": "Release Notes", "fileValidationFailed": "File validation failed", "insufficientStorage": "Insufficient storage space", "networkUnavailable": "Network connection unavailable", "wifiRequired": "WiFi connection required", "permissionRequired": "Installation permission required", "versionInfo": "Version Info"}