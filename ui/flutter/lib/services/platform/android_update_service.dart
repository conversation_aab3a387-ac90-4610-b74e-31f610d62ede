/// LEGALESE:   Copyright (c) 2025, UNISASE Corporation.
///
/// This source code is confidential, proprietary, and contains trade
/// secrets that are the sole property of UNISASE Corporation.
/// Copy and/or distribution of this source code or disassembly or reverse
/// engineering of the resultant object code are strictly forbidden without
/// the written consent of UNISASE Corporation LLC.
///
/// FILE NAME :      android_update_service.dart
///
/// DESCRIPTION :    Android平台特定的更新服务实现
///
/// AUTHOR :         wei
///
/// HISTORY :        04/08/2025 create

import 'dart:io';

import 'package:android_intent_plus/android_intent.dart';
import 'package:crypto/crypto.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';
import 'package:package_info_plus/package_info_plus.dart';
import 'package:path_provider/path_provider.dart';

import 'platform_update_service.dart';

/// AndroidUpdateService
///
/// PURPOSE:
///     Android平台特定的更新服务实现，处理APK安装包的下载和安装
///
/// FEATURES:
///     - APK安装意图调用
///     - 文件完整性验证（SHA-256）
///     - Android安装权限处理
///     - 外部存储目录管理
///     - 网络状态检测
///     - WiFi连接检查
///     - 存储空间检查
///
/// USAGE:
///     AndroidUpdateService service = AndroidUpdateService();
///     await service.installUpdate('/path/to/update.apk');
class AndroidUpdateService implements PlatformUpdateService {
  static const String _platformType = 'android';
  static const String _fileExtension = '.apk';

  @override
  Future<void> installUpdate(String filePath) async {
    if (!await File(filePath).exists()) {
      throw Exception('Update file not found: $filePath');
    }

    try {
      // 使用平台方法安装APK，通过FileProvider安全地共享文件
      const platform = MethodChannel('panabit_client/methods');
      final result = await platform.invokeMethod('installApk', {
        'filePath': filePath,
      });

      if (result != true) {
        throw Exception('APK installation failed');
      }
    } catch (e) {
      throw Exception('Failed to start APK installation: $e');
    }
  }

  @override
  Future<void> openAppStore(String appId) async {
    try {
      // 打开Google Play Store
      final intent = AndroidIntent(
        action: 'android.intent.action.VIEW',
        data: 'market://details?id=$appId',
      );

      await intent.launch();
    } catch (e) {
      // 如果Play Store不可用，尝试使用浏览器打开
      try {
        final webIntent = AndroidIntent(
          action: 'android.intent.action.VIEW',
          data: 'https://play.google.com/store/apps/details?id=$appId',
        );
        await webIntent.launch();
      } catch (e2) {
        throw Exception('Failed to open app store: $e2');
      }
    }
  }

  @override
  String getDownloadDirectory() {
    // Android上使用应用缓存目录，系统自动管理，无需手动清理
    return 'app_cache_storage/updates';
  }

  /// _getActualDownloadDirectory
  ///
  /// DESCRIPTION:
  ///     获取实际的下载目录路径，使用Android缓存目录
  ///     缓存目录由系统自动管理，无需手动清理
  ///
  /// RETURNS:
  ///     Future<String> - 实际的下载目录路径
  Future<String> _getActualDownloadDirectory() async {
    try {
      // 使用应用缓存目录，系统会自动管理空间
      final directory = await getTemporaryDirectory();
      final updateDir = '${directory.path}/updates';

      // 确保目录存在
      final dir = Directory(updateDir);
      if (!await dir.exists()) {
        await dir.create(recursive: true);
      }

      debugPrint('Using cache directory: $updateDir');
      return updateDir;
    } catch (e) {
      debugPrint('Cache directory access failed: $e');
      throw Exception('Unable to access cache directory: $e');
    }
  }



  @override
  Future<bool> validateFile(String filePath, String expectedHash, String hashType) async {
    try {
      final file = File(filePath);
      if (!await file.exists()) {
        return false;
      }

      // 读取文件内容
      final bytes = await file.readAsBytes();
      
      // 计算哈希值
      String actualHash;
      switch (hashType.toUpperCase()) {
        case 'SHA-256':
        case 'SHA256':
          actualHash = sha256.convert(bytes).toString();
          break;
        case 'MD5':
          actualHash = md5.convert(bytes).toString();
          break;
        default:
          throw Exception('Unsupported hash type: $hashType');
      }

      // 比较哈希值（忽略大小写）
      return actualHash.toLowerCase() == expectedHash.toLowerCase();
    } catch (e) {
      return false;
    }
  }

  @override
  Future<bool> checkPermissions() async {
    // Android上需要检查安装未知来源应用的权限和存储权限
    try {
      // 检查下载目录是否可访问
      final downloadDir = await _getActualDownloadDirectory();
      final directory = Directory(downloadDir);

      // 尝试创建目录来验证权限
      if (!await directory.exists()) {
        await directory.create(recursive: true);
      }

      // 尝试写入测试文件来验证权限
      final testFile = File('${directory.path}/.permission_test');
      await testFile.writeAsString('test');
      await testFile.delete();

      return true;
    } catch (e) {
      debugPrint('Android permission check failed: $e');
      return false;
    }
  }

  @override
  Future<bool> requestPermissions() async {
    // Android上请求安装权限
    try {
      // 首先检查存储权限
      if (!await checkPermissions()) {
        debugPrint('Storage permission check failed');
        return false;
      }

      // 打开设置页面让用户手动开启安装未知来源应用的权限
      const intent = AndroidIntent(
        action: 'android.settings.MANAGE_UNKNOWN_APP_SOURCES',
      );
      await intent.launch();
      return true;
    } catch (e) {
      debugPrint('Android permission request failed: $e');
      return false;
    }
  }

  @override
  bool canInstallUpdates() {
    return true;
  }

  @override
  String getFileExtension() {
    return _fileExtension;
  }

  @override
  Future<void> cleanupOldFiles({int keepLatest = 1}) async {
    // 使用缓存目录，系统会自动管理空间，无需手动清理
    debugPrint('Using cache directory - system will automatically manage space');
  }

  @override
  Future<int> getAvailableSpace() async {
    try {
      final downloadDirPath = await _getActualDownloadDirectory();
      final directory = Directory(downloadDirPath);

      // 确保目录存在
      if (!await directory.exists()) {
        await directory.create(recursive: true);
      }

      // Android上获取可用空间需要调用系统API
      // 这里是简化实现，返回一个估算值
      // 实际应用中可以使用platform channel调用Android API获取真实可用空间
      return 1024 * 1024 * 1024; // 1GB
    } catch (e) {
      return 0;
    }
  }

  @override
  Future<bool> isNetworkAvailable() async {
    try {
      // 尝试连接到一个可靠的服务器来检查网络连接
      final result = await InternetAddress.lookup('google.com');
      return result.isNotEmpty && result[0].rawAddress.isNotEmpty;
    } catch (e) {
      return false;
    }
  }

  @override
  Future<bool> isWifiConnected() async {
    // Android上检查WiFi连接需要调用系统API
    // 这里简化实现，假设有网络连接就检查是否为WiFi
    // 实际实现需要使用connectivity_plus包或调用Android API
    return await isNetworkAvailable();
  }

  @override
  Future<void> showUpdateNotification(String title, String message, {bool isForceUpdate = false}) async {
    // Android上的通知实现需要使用flutter_local_notifications包
    // 这里是简化实现，使用debugPrint避免生产环境输出
    debugPrint('Android Notification: $title - $message');
  }

  @override
  Future<void> hideUpdateNotification() async {
    // 隐藏通知的实现
    debugPrint('Android: Hide notification');
  }

  @override
  String getPlatformType() {
    return _platformType;
  }

  @override
  Future<String> getCurrentVersion() async {
    try {
      final packageInfo = await PackageInfo.fromPlatform();
      return packageInfo.version;
    } catch (e) {
      return '1.0.0'; // 默认版本
    }
  }

  @override
  String? getAppId() {
    // Android平台使用包名作为应用ID
    // 实际实现中应该从配置或PackageInfo获取
    return 'com.example.app'; // 占位符，实际应用中需要配置
  }

  @override
  Future<void> dispose() async {
    // Android平台特定的清理工作
    // 目前没有需要清理的资源
  }

  /// getActualDownloadDirectoryPath
  ///
  /// DESCRIPTION:
  ///     获取实际的下载目录路径，供外部调用
  ///
  /// RETURNS:
  ///     Future<String> - 实际的下载目录路径
  Future<String> getActualDownloadDirectoryPath() async {
    return await _getActualDownloadDirectory();
  }
}
