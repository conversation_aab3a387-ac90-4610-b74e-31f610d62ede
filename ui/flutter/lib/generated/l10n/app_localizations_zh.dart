// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for Chinese (`zh`).
class AppLocalizationsZh extends AppLocalizations {
  AppLocalizationsZh([String locale = 'zh']) : super(locale);

  @override
  String get appTitle => 'Panabit iWAN';

  @override
  String get login => '登录';

  @override
  String get username => '用户名';

  @override
  String get password => '密码';

  @override
  String get domain => '域名';

  @override
  String get rememberCredentials => '记住凭据';

  @override
  String get loginButton => '登录';

  @override
  String get loginFailed => '登录失败';

  @override
  String get loginSuccess => '登录成功';

  @override
  String get logout => '退出登录';

  @override
  String get connect => '连接';

  @override
  String get disconnect => '断开连接';

  @override
  String get connecting => '连接中...';

  @override
  String get connected => '已连接';

  @override
  String get disconnected => '未连接';

  @override
  String get disconnecting => '断开中...';

  @override
  String get reconnecting => '重连中...';

  @override
  String get connectionFailed => '连接失败';

  @override
  String get connectionSuccess => '连接成功';

  @override
  String get serverList => '服务器列表';

  @override
  String get selectServer => '选择服务器';

  @override
  String get noServersAvailable => '没有可用服务器';

  @override
  String get refreshServers => '刷新服务器';

  @override
  String get settings => '设置';

  @override
  String get about => '关于';

  @override
  String get statistics => '统计';

  @override
  String get logs => '日志';

  @override
  String get language => '语言';

  @override
  String get theme => '主题';

  @override
  String get autoConnect => '自动连接';

  @override
  String get minimizeToTray => '最小化到托盘';

  @override
  String get startWithSystem => '开机启动';

  @override
  String get connectionTime => '连接时间';

  @override
  String get dataTransferred => '数据传输';

  @override
  String get ping => '延迟';

  @override
  String get latency => '延迟';

  @override
  String get version => '版本';

  @override
  String get copyright => '版权';

  @override
  String get termsOfService => '服务条款';

  @override
  String get privacyPolicy => '隐私政策';

  @override
  String get acceptLicense => '我接受服务条款和隐私政策';

  @override
  String get licenseRequired => '您必须接受许可协议才能继续';

  @override
  String get error => '错误';

  @override
  String get warning => '警告';

  @override
  String get info => '信息';

  @override
  String get success => '成功';

  @override
  String get cancel => '取消';

  @override
  String get ok => '确定';

  @override
  String get yes => '是';

  @override
  String get no => '否';

  @override
  String get save => '保存';

  @override
  String get close => '关闭';

  @override
  String get exit => '退出';

  @override
  String get minimize => '最小化';

  @override
  String get maximize => '最大化';

  @override
  String get restore => '还原';

  @override
  String get copy => '复制';

  @override
  String get paste => '粘贴';

  @override
  String get cut => '剪切';

  @override
  String get selectAll => '全选';

  @override
  String get clear => '清除';

  @override
  String get refresh => '刷新';

  @override
  String get retry => '重试';

  @override
  String get loading => '加载中...';

  @override
  String get pleaseWait => '请稍候...';

  @override
  String get networkError => '网络错误';

  @override
  String get serverError => '服务器错误';

  @override
  String get connectionTimeout => '连接超时';

  @override
  String get invalidCredentials => '凭据无效';

  @override
  String get sessionExpired => '会话已过期';

  @override
  String get accessDenied => '访问被拒绝';

  @override
  String get serviceUnavailable => '服务不可用';

  @override
  String get mainScreen => '主页';

  @override
  String get connectionScreen => '连接';

  @override
  String get userScreen => '用户';

  @override
  String get settingsScreen => '设置';

  @override
  String get aboutScreen => '关于';

  @override
  String get logsScreen => '日志';

  @override
  String get statisticsScreen => '统计';

  @override
  String get enterServerDomain => '输入服务器域名';

  @override
  String get serverDomain => '服务器域名';

  @override
  String get serverDomainHint => '例如: vpn.example.com';

  @override
  String get nextStep => '下一步';

  @override
  String get clientDomain => '客户域';

  @override
  String get change => '更改';

  @override
  String get rememberUsernamePassword => '记住用户名和密码';

  @override
  String get pleaseEnterServerDomain => '请输入服务器域名';

  @override
  String get pleaseEnterUsername => '请输入用户名';

  @override
  String get pleaseEnterPassword => '请输入密码';

  @override
  String get pleaseEnterClientDomain => '请输入客户域名';

  @override
  String get clientDomainHint => '例如: research.staff.unisase';

  @override
  String get lookupServiceError => '查询服务器地址失败';

  @override
  String get lookupServiceTimeout => '查询服务器地址超时';

  @override
  String get lookupServiceInvalidResponse => '服务器返回无效响应';

  @override
  String get lookupServiceNetworkError => '网络连接失败，请检查网络设置';

  @override
  String get queryingServerAddress => '正在查询服务器地址...';

  @override
  String get startingBackendService => '正在启动后端服务...';

  @override
  String get backendServiceStartFailed => '启动后端服务失败，请检查权限或重启应用';

  @override
  String get checkingBackendServiceStatus => '启动中...';

  @override
  String get backendServiceHealthCheckFailed => '后端服务健康检查失败，请检查端口是否被占用或重启应用程序';

  @override
  String get autoStart => '开机自启动';

  @override
  String get autoStartEnabled => '开机自启动已开启';

  @override
  String get autoStartDisabled => '开机自启动已关闭';

  @override
  String get routingSettings => '路由设置';

  @override
  String get applying => '应用中...';

  @override
  String get applySettings => '应用设置';

  @override
  String get settingsAppliedSuccessfully => '设置已成功应用';

  @override
  String get noChangesToApply => '设置无变更，无需应用';

  @override
  String get getRoutingSettingsFailed => '获取路由设置失败';

  @override
  String get saveAutoStartSettingFailed => '保存自动启动设置失败';

  @override
  String get saveRoutingSettingsFailed => '保存路由设置失败';

  @override
  String get appSettings => '应用设置';

  @override
  String get appName => '应用名称';

  @override
  String get versionNumber => '版本号';

  @override
  String get deviceId => '设备ID';

  @override
  String get gettingDeviceId => '获取中...';

  @override
  String get getDeviceIdFailed => '获取失败';

  @override
  String get agreementsAndContact => '协议条款';

  @override
  String get viewTermsOfService => '查看用户服务协议';

  @override
  String get viewPrivacyPolicy => '查看隐私保护政策';

  @override
  String get officialWebsite => '官方网站';

  @override
  String get technicalSupport => '技术支持';

  @override
  String get openLinkFailed => '打开链接失败';

  @override
  String get clickToView => '点击查看';

  @override
  String get sendEmailFailed => '发送邮件失败';

  @override
  String get vpnClientFeedback => 'WAN客户端反馈';

  @override
  String get allUrlLaunchMethodsFailed => '所有URL启动方法都失败了';

  @override
  String get openLinkFailedWithError => '打开链接失败';

  @override
  String get sendEmailFailedWithError => '发送邮件失败';

  @override
  String get statisticsInfo => '统计信息';

  @override
  String get status => '状态';

  @override
  String get interface => '接口';

  @override
  String get upload => '上行';

  @override
  String get download => '下行';

  @override
  String get localIp => '本地IP';

  @override
  String get itforceIp => '云IP';

  @override
  String get personalInfo => '个人信息';

  @override
  String get editPersonalInfo => '编辑个人信息';

  @override
  String get name => '姓名';

  @override
  String get pleaseEnterName => '请输入姓名';

  @override
  String get department => '部门';

  @override
  String get position => '职位';

  @override
  String get accountInfo => '账户信息';

  @override
  String get clientDomainLabel => '客户域';

  @override
  String get usernameLabel => '用户名';

  @override
  String get deviceInfo => '设备ID';

  @override
  String get logoutButton => '注销';

  @override
  String get personalInfoSaved => '个人信息已保存';

  @override
  String get saveFailed => '保存失败，请重试';

  @override
  String get notSet => '未设置';

  @override
  String get editUserInfo => '编辑个人信息';

  @override
  String get confirmLogout => '确认注销';

  @override
  String get logoutConfirmation => '确定要注销吗？';

  @override
  String get logoutWithVpnWarning => '确定要注销吗？\n\n注意：当前WAN连接将会自动断开。';

  @override
  String get disconnectAndExit => '断开并退出';

  @override
  String get clearLogs => '清除日志';

  @override
  String get confirmClearLogs => '确定要清除所有日志吗？';

  @override
  String get confirm => '确定';

  @override
  String get confirmReconnection => '确认重连';

  @override
  String get routingChangeRequiresReconnection =>
      '当前VPN已连接，更改路由设置需要重新连接，是否确认并重连？';

  @override
  String get confirmAndReconnect => '确认并重连';

  @override
  String get routingSettingsAppliedAndReconnected => '路由设置已保存并重新连接';

  @override
  String get routingSettingsAppliedDisconnected => '路由设置已保存，VPN已断开';

  @override
  String get routingSettingsReconnectionFailed => '路由设置重连失败';

  @override
  String get logsExportedTo => '日志已导出到';

  @override
  String get exportLogsFailed => '导出日志失败';

  @override
  String get logCopiedToClipboard => '日志已复制到剪贴板';

  @override
  String get allLevels => '所有级别';

  @override
  String get searchLogs => '搜索日志...';

  @override
  String get logsTitle => '日志';

  @override
  String get closeSearch => '关闭搜索';

  @override
  String get searchLogsTooltip => '搜索日志';

  @override
  String get filterByLevel => '按级别筛选';

  @override
  String get moreActions => '更多操作';

  @override
  String get exportLogs => '导出日志';

  @override
  String get filterPrefix => '筛选: ';

  @override
  String get clearFilter => '清除筛选';

  @override
  String get noLogs => '没有日志';

  @override
  String get noData => '暂无数据';

  @override
  String get scrollToLatestLog => '滚动到最新日志';

  @override
  String get connectionManagement => '连接管理';

  @override
  String get userInfo => '用户信息';

  @override
  String get trafficStatistics => '流量统计';

  @override
  String get systemLogs => '系统日志';

  @override
  String get aboutApp => '关于应用';

  @override
  String get connection => '连接';

  @override
  String get user => '用户';

  @override
  String get statisticsNav => '统计';

  @override
  String get settingsNav => '设置';

  @override
  String get logsNav => '日志';

  @override
  String get aboutNav => '关于';

  @override
  String get auto => '自动';

  @override
  String get statusUpdated => '状态已更新';

  @override
  String get routingMode => '路由模式';

  @override
  String get allRouting => '全部路由';

  @override
  String get allRoutingDescription => '所有流量都通过WAN隧道';

  @override
  String get customRouting => '按网段路由';

  @override
  String get customRoutingDescription => '仅指定网段的流量通过WAN隧道';

  @override
  String get enterNetworkSegments => '请输入需要路由的网段';

  @override
  String get networkSegmentsExample =>
      '多个网段用逗号分隔，例如: ***********/16,10.0.0.0/8';

  @override
  String get enterNetworkSegmentsHint => '输入网段...';

  @override
  String get ensureCorrectCidrFormat => '请确保输入正确的CIDR格式';

  @override
  String get uploadSpeed => '上行速率';

  @override
  String get downloadSpeed => '下行速率';

  @override
  String get unreachable => '不可达';

  @override
  String get excellent => '极佳';

  @override
  String get good => '良好';

  @override
  String get poor => '较差';

  @override
  String get languageSettings => '语言设置';

  @override
  String get selectLanguage => '选择语言';

  @override
  String get chinese => '中文';

  @override
  String get english => 'English';

  @override
  String get languageChanged => '语言已更改';

  @override
  String get pleaseSelectServer => '请先选择服务器';

  @override
  String get connectingToServer => '正在连接...';

  @override
  String get disconnectingFromServer => '正在断开连接...';

  @override
  String get connectionTimeoutDetailed => '连接超时，请检查网络连接或稍后重试';

  @override
  String get connectionFailedGeneric => '连接失败';

  @override
  String get disconnectedFromServer => '已断开连接';

  @override
  String switchingToServer(Object serverName) {
    return '正在切换到 $serverName...';
  }

  @override
  String connectedToServer(Object serverName) {
    return '已连接到 $serverName';
  }

  @override
  String currentlyConnectedTo(Object newServerName, Object serverName) {
    return '当前连接到 $serverName，正在切换到 $newServerName';
  }

  @override
  String get selectServerFirst => '请先选择服务器';

  @override
  String get operationTimeout => '操作超时，请稍后重试';

  @override
  String pingServersFailed(Object error) {
    return '测试延迟失败: $error';
  }

  @override
  String get networkConnectionFailed => '网络连接失败，请检查网络设置';

  @override
  String get realtimeConnectionInterrupted => '实时连接中断，正在尝试重新连接...';

  @override
  String get authenticationFailed => '认证失败，请重新登录';

  @override
  String get operationFailedRetry => '操作失败，请稍后重试';

  @override
  String get systemTrayConnected => '已连接';

  @override
  String get systemTrayConnecting => '正在连接...';

  @override
  String get systemTrayDisconnecting => '正在断开...';

  @override
  String get systemTrayDisconnected => '未连接';

  @override
  String get showWindow => '显示窗口';

  @override
  String get hideWindow => '隐藏窗口';

  @override
  String get exitApp => '退出应用';

  @override
  String get processing => '处理中...';

  @override
  String get calculating => '计算中...';

  @override
  String get operationCancelled => '操作已取消';

  @override
  String get testLatency => '测试延迟';

  @override
  String get testingLatency => '正在测试延迟...';

  @override
  String get latencyTestComplete => '延迟测试完成';

  @override
  String currentlyConnectedToServer(Object serverName) {
    return '当前连接到: $serverName';
  }

  @override
  String get unknownServer => '未知服务器';

  @override
  String get noAutoServersAvailable => '没有可用的自动分流服务器，请检查网络连接或联系管理员';

  @override
  String get autoServerSelectionFailed => '自动服务器选择失败，请手动选择服务器或稍后重试';

  @override
  String get apiInvalidRequest => '无效的请求格式或参数';

  @override
  String get apiInvalidCredentials => '用户名或密码错误';

  @override
  String get apiServerError => '服务器内部错误';

  @override
  String get apiResourceNotFound => '请求的资源不存在';

  @override
  String get apiUnauthorized => '未授权访问，请重新登录';

  @override
  String get apiForbidden => '禁止访问该资源';

  @override
  String get apiTimeout => '请求超时，请稍后重试';

  @override
  String get apiConflict => '资源冲突';

  @override
  String get apiRateLimit => '请求过于频繁，请稍后重试';

  @override
  String get apiGatewayError => '网关错误';

  @override
  String get apiServiceUnavailable => '服务暂时不可用，请稍后重试';

  @override
  String get networkUnreachable => '网络不可达，请检查网络连接';

  @override
  String get networkDnsFailure => 'DNS解析失败，请检查服务器地址';

  @override
  String get networkConnectionReset => '网络连接被重置';

  @override
  String get networkConnectionClosed => '网络连接已关闭';

  @override
  String get networkProxyError => '代理服务器错误';

  @override
  String get networkTlsError => 'TLS/SSL错误';

  @override
  String get authInvalidCredentials => '无效的用户凭据';

  @override
  String get authExpiredCredentials => '凭据已过期，请重新登录';

  @override
  String get authRateLimit => '认证请求过于频繁，请稍后重试';

  @override
  String get authAccountLocked => '账户已锁定，请联系管理员';

  @override
  String get authInvalidToken => '无效的认证令牌';

  @override
  String get authTokenExpired => '认证令牌已过期，请重新登录';

  @override
  String get authMissingCredentials => '缺少认证凭据';

  @override
  String get tunnelError => '隧道错误';

  @override
  String get tunnelInitFailed => '隧道初始化失败';

  @override
  String get tunnelCloseFailed => '隧道关闭失败';

  @override
  String get tunnelReadFailed => '隧道读取失败';

  @override
  String get tunnelWriteFailed => '隧道写入失败';

  @override
  String get tunnelConfigFailed => '隧道配置失败';

  @override
  String get configError => '配置错误';

  @override
  String get configInvalid => '无效的配置';

  @override
  String get configFileNotFound => '配置文件不存在';

  @override
  String get configFileReadFailed => '配置文件读取失败';

  @override
  String get configFileWriteFailed => '配置文件写入失败';

  @override
  String get configFileParseFailed => '配置文件解析失败';

  @override
  String get platformError => '平台错误';

  @override
  String get platformUnsupported => '不支持的平台';

  @override
  String get platformInitFailed => '平台初始化失败';

  @override
  String get platformIoError => '平台IO错误';

  @override
  String get platformPermissionDenied => '权限被拒绝，请以管理员身份运行';

  @override
  String get domainLookupFailed => '域名查找失败';

  @override
  String get domainNotFound => '域名不存在，请检查域名是否正确';

  @override
  String get domainInvalid => '域名格式无效';

  @override
  String get domainRequired => '域名参数不能为空';

  @override
  String get domainLookupTimeout => '域名查找超时，请稍后重试';

  @override
  String get domainLookupNetworkError => '域名查找网络错误，请检查网络连接';

  @override
  String get serverListNotFound => '服务器列表未找到，请检查服务器地址';

  @override
  String get serverListInvalid => '服务器列表格式无效';

  @override
  String get serverListTimeout => '服务器列表请求超时，请稍后重试';

  @override
  String get serverListNetworkError => '获取服务器列表时网络错误，请检查网络连接';

  @override
  String get protocolError => '协议错误';

  @override
  String get protocolInvalid => '无效的协议';

  @override
  String get protocolUnsupported => '不支持的协议';

  @override
  String get protocolVersionMismatch => '协议版本不匹配';

  @override
  String get protocolHandshakeFailed => '协议握手失败';

  @override
  String get protocolEncryptionFailed => '协议加密失败';

  @override
  String get protocolDecryptionFailed => '协议解密失败';

  @override
  String get unknownError => '未知错误';

  @override
  String get invalidParameter => '无效参数';

  @override
  String get notImplemented => '功能未实现';

  @override
  String get permissionDenied => '权限被拒绝';

  @override
  String get checkUpdate => '检查更新';

  @override
  String get checkingUpdate => '检查更新中...';

  @override
  String get updateAvailable => '发现新版本';

  @override
  String get updateNotAvailable => '已是最新版本';

  @override
  String get currentVersion => '当前版本';

  @override
  String get latestVersion => '最新版本';

  @override
  String get updateNow => '立即更新';

  @override
  String get updateLater => '稍后提醒';

  @override
  String get skipUpdate => '跳过更新';

  @override
  String get downloading => '下载中...';

  @override
  String get downloadProgress => '下载进度';

  @override
  String get downloadComplete => '下载完成';

  @override
  String get downloadFailed => '下载失败';

  @override
  String get installing => '安装中...';

  @override
  String get installComplete => '安装完成';

  @override
  String get installFailed => '安装失败';

  @override
  String get installStarted => '安装程序已启动，请在系统对话框中点击\'安装\'来完成应用更新。安装完成后请手动重启应用。';

  @override
  String get updateFailed => '更新失败';

  @override
  String get updateCancelled => '更新已取消';

  @override
  String get forceUpdate => '强制更新';

  @override
  String get forceUpdateMessage => '此更新包含重要的安全修复，必须立即更新';

  @override
  String get releaseNotes => '更新日志';

  @override
  String get fileValidationFailed => '文件校验失败';

  @override
  String get insufficientStorage => '存储空间不足';

  @override
  String get networkUnavailable => '网络连接不可用';

  @override
  String get wifiRequired => '需要WiFi连接';

  @override
  String get permissionRequired => '需要安装权限';

  @override
  String get versionInfo => '版本信息';
}
