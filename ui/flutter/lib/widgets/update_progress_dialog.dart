/// LEGALESE:   Copyright (c) 2025, UNISASE Corporation.
///
/// This source code is confidential, proprietary, and contains trade
/// secrets that are the sole property of UNISASE Corporation.
/// Copy and/or distribution of this source code or disassembly or reverse
/// engineering of the resultant object code are strictly forbidden without
/// the written consent of UNISASE Corporation LLC.
///
/// FILE NAME :      update_progress_dialog.dart
///
/// DESCRIPTION :    更新进度对话框，显示下载和安装进度
///
/// AUTHOR :         wei
///
/// HISTORY :        01/04/2025 create

import 'dart:async';
import 'package:flutter/material.dart';

import '../services/update_service.dart';
import '../models/update_config.dart';
import '../models/update_info.dart';
import '../models/update_status.dart';
import '../core/dependency_injection.dart';
import '../services/log_service.dart';
import '../generated/l10n/app_localizations.dart';

/// UpdateProgressDialog
///
/// PURPOSE:
///     更新进度对话框，显示下载和安装进度
///
/// FEATURES:
///     - 实时显示下载进度
///     - 显示当前状态（检查中、下载中、安装中）
///     - 支持取消下载
///     - 错误处理和重试
class UpdateProgressDialog extends StatefulWidget {
  final dynamic updateInfo;

  const UpdateProgressDialog({
    Key? key,
    required this.updateInfo,
  }) : super(key: key);

  @override
  State<UpdateProgressDialog> createState() => _UpdateProgressDialogState();
}

class _UpdateProgressDialogState extends State<UpdateProgressDialog> {
  late UpdateService _updateService;
  late StreamSubscription<UpdateInfo> _updateSubscription;
  late LogService _logService;
  
  UpdateInfo _currentUpdateInfo = UpdateInfo.noUpdate();
  String _statusText = '准备中...';
  bool _hasError = false;
  String _errorMessage = '';
  bool _isCompleted = false;
  bool _isCancelled = false; // 添加取消状态标记

  @override
  void initState() {
    super.initState();
    _logService = serviceLocator<LogService>();
    _initializeUpdateService();
  }

  @override
  void dispose() {
    _updateSubscription.cancel();
    _updateService.dispose();
    super.dispose();
  }

  /// 初始化更新服务
  void _initializeUpdateService() async {
    try {
      // 创建更新服务实例（禁用自动检查）
      final config = UpdateConfig.defaultConfig().copyWith(
        enableAutoCheck: false,
        checkOnStartup: false,
      );
      _updateService = UpdateService(config: config);
      
      await _updateService.initialize();
      
      // 监听更新状态变化
      _updateSubscription = _updateService.updateStream.listen(
        _onUpdateInfoChanged,
        onError: _onUpdateError,
      );
      
      // 开始下载（异步调用，但不等待）
      _startDownload().catchError((e) {
        _logService.error('UpdateProgressDialog', 'Failed to start download', e);
        if (mounted) {
          _onUpdateError(e);
        }
      });
    } catch (e) {
      _logService.error('UpdateProgressDialog', 'Failed to initialize update service', e);
      _onUpdateError(e);
    }
  }

  /// 开始下载更新
  Future<void> _startDownload() async {
    try {
      setState(() {
        _statusText = '验证更新信息...';
        _hasError = false;
      });

      _logService.info('UpdateProgressDialog', 'Starting download for version: ${widget.updateInfo.version}');
      _logService.debug('UpdateProgressDialog', 'UpdateInfo status: ${widget.updateInfo.status}, canDownload: ${widget.updateInfo.canDownload}');

      // 检查是否可以下载
      if (!widget.updateInfo.canDownload) {
        throw Exception('无法下载更新：状态无效或缺少必要信息。状态: ${widget.updateInfo.status}');
      }

      setState(() {
        _statusText = '开始下载...';
      });

      await _updateService.downloadUpdate(widget.updateInfo);
    } catch (e) {
      // 检查是否是用户取消
      if (e is UpdateCancelledException) {
        _logService.info('UpdateProgressDialog', 'Download cancelled by user');
        // 用户取消，只记录日志，不关闭对话框（已在_cancelUpdate中关闭）
        if (!_isCancelled) {
          // 如果不是通过按钮取消的（比如网络超时导致的取消），则关闭对话框
          if (mounted && Navigator.canPop(context)) {
            Navigator.of(context).pop();
          }
        }
        return;
      } else {
        _logService.error('UpdateProgressDialog', 'Download failed - Error: $e');
        _onUpdateError(e);
      }
    }
  }

  /// 处理更新信息变化
  void _onUpdateInfoChanged(UpdateInfo updateInfo) {
    if (!mounted) return;
    
    setState(() {
      _currentUpdateInfo = updateInfo;
      _hasError = false;
      
      final l10n = AppLocalizations.of(context)!;

      switch (updateInfo.status) {
        case UpdateStatus.checking:
          _statusText = l10n.checkingUpdate;
          break;
        case UpdateStatus.downloading:
          if (updateInfo.downloadProgress == 1.0) {
            _statusText = l10n.downloadComplete;
          } else {
            _statusText = l10n.downloading;
          }
          break;
        case UpdateStatus.downloaded:
          _statusText = l10n.downloadComplete;
          _startInstallation();
          break;
        case UpdateStatus.installing:
          // Android平台特殊处理：显示授权安装提示
          if (updateInfo.errorMessage == 'install_started') {
            _statusText = l10n.installStarted;
            _isCompleted = true; // 显示关闭按钮，让用户可以关闭对话框
          } else {
            _statusText = l10n.installing;
          }
          break;
        case UpdateStatus.installed:
          _statusText = l10n.installComplete;
          _isCompleted = true;
          break;
        case UpdateStatus.failed:
          _statusText = l10n.updateFailed;
          _hasError = true;
          _errorMessage = updateInfo.errorMessage ?? l10n.unknownError;
          break;
        default:
          _statusText = l10n.checkingUpdate;
      }
    });
  }

  /// 开始安装
  void _startInstallation() async {
    try {
      await _updateService.installUpdate();
    } catch (e) {
      _logService.error('UpdateProgressDialog', 'Installation failed', e);
      _onUpdateError(e);
    }
  }

  /// 处理更新错误
  void _onUpdateError(dynamic error) {
    if (!mounted) return;
    
    setState(() {
      _hasError = true;
      _errorMessage = error.toString();
      _statusText = '更新失败';
    });
  }

  /// 重试更新
  void _retryUpdate() {
    setState(() {
      _hasError = false;
      _errorMessage = '';
      _isCompleted = false;
    });
    _startDownload();
  }

  /// 取消更新
  void _cancelUpdate() {
    if (_isCancelled) {
      return; // 防止重复取消
    }

    _isCancelled = true;
    _updateService.cancelUpdate();

    // 安全地关闭对话框
    if (mounted && Navigator.canPop(context)) {
      Navigator.of(context).pop();
    }
  }

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context)!;

    return AlertDialog(
      title: Text(l10n.downloadProgress),
      content: SizedBox(
        width: 300,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // 状态文本
            Text(
              _statusText,
              style: const TextStyle(fontSize: 16),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 20),

            // 进度指示器
            if (!_hasError && !_isCompleted) ...[
              // 使用CircularProgressIndicator显示不确定进度，LinearProgressIndicator显示确定进度
              if (_currentUpdateInfo.downloadProgress > 0) ...[
                // 确定进度 - 使用LinearProgressIndicator
                LinearProgressIndicator(
                  value: _currentUpdateInfo.downloadProgress,
                  backgroundColor: Colors.grey[300],
                  valueColor: AlwaysStoppedAnimation<Color>(
                    Theme.of(context).primaryColor,
                  ),
                  minHeight: 8,
                ),
                const SizedBox(height: 8),
                // 只在进度条下方显示百分比
                Text(
                  '${(_currentUpdateInfo.downloadProgress * 100).toStringAsFixed(1)}%',
                  style: TextStyle(
                    color: Colors.grey[600],
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                  ),
                  textAlign: TextAlign.center,
                ),
              ] else ...[
                // 不确定进度 - 使用CircularProgressIndicator
                CircularProgressIndicator(
                  valueColor: AlwaysStoppedAnimation<Color>(
                    Theme.of(context).primaryColor,
                  ),
                ),
              ],
            ],

            // 错误状态
            if (_hasError) ...[
              Icon(
                Icons.error_outline,
                color: Colors.red[600],
                size: 56,
              ),
              const SizedBox(height: 16),
              Text(
                _errorMessage,
                style: TextStyle(
                  color: Colors.red[600],
                  fontSize: 14,
                ),
                textAlign: TextAlign.center,
              ),
            ],

            // 完成状态
            if (_isCompleted) ...[
              Icon(
                Icons.check_circle,
                color: Colors.green[600],
                size: 56,
              ),
              const SizedBox(height: 16),
              const Text(
                '更新安装完成，请重启应用以使用新版本。',
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                ),
                textAlign: TextAlign.center,
              ),
            ],

            const SizedBox(height: 8),
          ],
        ),
      ),
      actions: [
        // 取消按钮（在下载中或准备中显示）
        if (!_hasError && !_isCompleted)
          TextButton(
            onPressed: _cancelUpdate,
            child: Text(l10n.cancel),
          ),

        // 重试按钮（仅在错误时显示）
        if (_hasError)
          ElevatedButton(
            onPressed: _retryUpdate,
            style: ElevatedButton.styleFrom(
              backgroundColor: Theme.of(context).primaryColor,
              foregroundColor: Colors.white,
            ),
            child: Text(l10n.retry),
          ),

        // 关闭按钮（仅在错误时显示）
        if (_hasError)
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: Text(l10n.close),
          ),

        // 完成按钮（仅在完成时显示）
        if (_isCompleted)
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.green[600],
              foregroundColor: Colors.white,
            ),
            child: Text(l10n.ok),
          ),
      ],
    );
  }
}
